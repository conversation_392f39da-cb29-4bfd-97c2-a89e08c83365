using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using ScoreboardCsvIngestor.Services;
using System.Net;
using System.Text.Json;
using System.Diagnostics;

namespace ScoreboardCsvIngestor.Functions;

public class CsvIngestorFunction
{
    private readonly ILogger<CsvIngestorFunction> _logger;
    private readonly ICsvParserService _csvParserService;
    private readonly IScoreboardDataService _dataService;

    public CsvIngestorFunction(
        ILogger<CsvIngestorFunction> logger,
        ICsvParserService csvParserService,
        IScoreboardDataService dataService)
    {
        _logger = logger;
        _csvParserService = csvParserService;
        _dataService = dataService;
    }

    [Function("ProcessCsvUpload")]
    public async Task<HttpResponseData> ProcessCsvUpload(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "csv/upload")] HttpRequestData req)
    {
        var correlationId = Guid.NewGuid().ToString();
        var stopwatch = Stopwatch.StartNew();

        _logger.LogInformation("CSV processing request started. CorrelationId: {CorrelationId}", correlationId);

        try
        {
            // Validate request
            if (req.Body == null)
            {
                _logger.LogWarning("Request body is null. CorrelationId: {CorrelationId}", correlationId);
                return await CreateErrorResponse(req, HttpStatusCode.BadRequest, "Request body is required", correlationId);
            }

            // Log request metadata for monitoring
            var contentLength = req.Headers.TryGetValues("Content-Length", out var lengthValues)
                ? lengthValues.FirstOrDefault() : "unknown";
            var userAgent = req.Headers.TryGetValues("User-Agent", out var agentValues)
                ? agentValues.FirstOrDefault() : "unknown";

            _logger.LogInformation("Processing CSV request - ContentLength: {ContentLength}, UserAgent: {UserAgent}, CorrelationId: {CorrelationId}",
                contentLength, userAgent, correlationId);

            // Test database connection first
            if (!await _dataService.TestConnectionAsync())
            {
                _logger.LogError("Database connection test failed. CorrelationId: {CorrelationId}", correlationId);
                return await CreateErrorResponse(req, HttpStatusCode.ServiceUnavailable, "Database connection failed", correlationId);
            }

            // Parse CSV content
            _logger.LogInformation("Starting CSV parsing. CorrelationId: {CorrelationId}", correlationId);
            var parseResult = await _csvParserService.ParseCsvAsync(req.Body);

            if (!parseResult.IsSuccess)
            {
                _logger.LogWarning("CSV parsing failed. Errors: {Errors}. CorrelationId: {CorrelationId}", 
                    string.Join(", ", parseResult.Errors), correlationId);
                
                return await CreateErrorResponse(req, HttpStatusCode.BadRequest, 
                    "CSV parsing failed", correlationId, parseResult.Errors, parseResult.ValidationErrors);
            }

            if (parseResult.Records.Count == 0)
            {
                _logger.LogWarning("No valid records found in CSV. CorrelationId: {CorrelationId}", correlationId);
                return await CreateErrorResponse(req, HttpStatusCode.BadRequest, "No valid records found in CSV", correlationId);
            }

            // Insert records into database
            _logger.LogInformation("Starting database insertion for {RecordCount} records. CorrelationId: {CorrelationId}", 
                parseResult.Records.Count, correlationId);
            
            var insertResult = await _dataService.InsertRecordsAsync(parseResult.Records);

            if (!insertResult.IsSuccess)
            {
                _logger.LogError("Database insertion failed. Errors: {Errors}. CorrelationId: {CorrelationId}", 
                    string.Join(", ", insertResult.Errors), correlationId);
                
                return await CreateErrorResponse(req, HttpStatusCode.InternalServerError, 
                    "Database insertion failed", correlationId, insertResult.Errors);
            }

            // Create success response
            var successResponse = new
            {
                success = true,
                correlationId,
                message = "CSV processed successfully",
                data = new
                {
                    totalRowsProcessed = parseResult.TotalRowsProcessed,
                    validRowsCount = parseResult.ValidRowsCount,
                    invalidRowsCount = parseResult.InvalidRowsCount,
                    recordsInserted = insertResult.RecordsInserted,
                    processingTimeMs = insertResult.ProcessingTime.TotalMilliseconds
                },
                timestamp = DateTime.UtcNow
            };

            stopwatch.Stop();

            _logger.LogInformation("CSV processing completed successfully. Records inserted: {RecordsInserted}, Duration: {DurationMs}ms, CorrelationId: {CorrelationId}",
                insertResult.RecordsInserted, stopwatch.ElapsedMilliseconds, correlationId);

            // Log custom metrics for Application Insights
            using var activity = new Activity("CsvProcessing");
            activity.SetTag("correlationId", correlationId);
            activity.SetTag("recordsInserted", insertResult.RecordsInserted.ToString());
            activity.SetTag("processingTimeMs", stopwatch.ElapsedMilliseconds.ToString());
            activity.SetTag("validRowsCount", parseResult.ValidRowsCount.ToString());
            activity.SetTag("invalidRowsCount", parseResult.InvalidRowsCount.ToString());

            var response = req.CreateResponse(HttpStatusCode.OK);
            response.Headers.Add("Content-Type", "application/json");
            await response.WriteStringAsync(JsonSerializer.Serialize(successResponse, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            }));

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during CSV processing. CorrelationId: {CorrelationId}", correlationId);
            return await CreateErrorResponse(req, HttpStatusCode.InternalServerError, 
                "An unexpected error occurred", correlationId, new List<string> { ex.Message });
        }
    }

    [Function("HealthCheck")]
    public async Task<HttpResponseData> HealthCheck(
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "health")] HttpRequestData req)
    {
        _logger.LogInformation("Health check requested");

        try
        {
            var dbConnectionOk = await _dataService.TestConnectionAsync();
            
            var healthStatus = new
            {
                status = dbConnectionOk ? "healthy" : "unhealthy",
                timestamp = DateTime.UtcNow,
                checks = new
                {
                    database = dbConnectionOk ? "ok" : "failed"
                }
            };

            var statusCode = dbConnectionOk ? HttpStatusCode.OK : HttpStatusCode.ServiceUnavailable;
            var response = req.CreateResponse(statusCode);
            response.Headers.Add("Content-Type", "application/json");
            await response.WriteStringAsync(JsonSerializer.Serialize(healthStatus, new JsonSerializerOptions 
            { 
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase 
            }));

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during health check");
            return await CreateErrorResponse(req, HttpStatusCode.InternalServerError, "Health check failed", Guid.NewGuid().ToString());
        }
    }

    private async Task<HttpResponseData> CreateErrorResponse(
        HttpRequestData req, 
        HttpStatusCode statusCode, 
        string message, 
        string correlationId,
        List<string>? errors = null,
        List<ValidationError>? validationErrors = null)
    {
        var errorResponse = new
        {
            success = false,
            correlationId,
            message,
            errors = errors ?? new List<string>(),
            validationErrors = validationErrors?.Select(ve => new
            {
                rowNumber = ve.RowNumber,
                fieldName = ve.FieldName,
                errorMessage = ve.ErrorMessage,
                invalidValue = ve.InvalidValue
            }).Cast<object>().ToList() ?? new List<object>(),
            timestamp = DateTime.UtcNow
        };

        var response = req.CreateResponse(statusCode);
        response.Headers.Add("Content-Type", "application/json");
        await response.WriteStringAsync(JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions 
        { 
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase 
        }));

        return response;
    }
}
