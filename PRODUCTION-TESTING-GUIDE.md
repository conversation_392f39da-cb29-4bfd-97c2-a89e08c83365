# 🧪 Production Testing Guide

This guide provides comprehensive testing procedures to validate that your Scoreboard CSV Ingestor Azure Function is working correctly with Logic Apps and Azure SQL database.

## 📋 Pre-Deployment Checklist

Before testing, ensure the following are configured:

- [ ] Azure Function App is deployed and running
- [ ] SQL connection string points to the `proof-of-concept` database
- [ ] `EveryCarChamps` table exists in the target database
- [ ] Application Insights is configured (optional but recommended)
- [ ] Function keys are available for Logic App integration

## 🔍 Test 1: Health Check

### Purpose
Verify the function is running and can connect to the database.

### Steps
1. **Direct HTTP Test:**
   ```bash
   curl -X GET "https://your-function-app.azurewebsites.net/api/health"
   ```

2. **Expected Response:**
   ```json
   {
     "status": "healthy",
     "timestamp": "2024-01-01T12:00:00.000Z",
     "checks": {
       "database": "ok"
     }
   }
   ```

3. **Troubleshooting:**
   - If `status: "unhealthy"` → Check SQL connection string
   - If 404 error → Verify function deployment
   - If timeout → Check function app is running

## 🧪 Test 2: CSV Processing Function

### Purpose
Test the core CSV processing functionality with valid data.

### Test Data
Create a test CSV file (`test-data.csv`):
```csv
Store Name,Lane Departure Time,Car Type,Lane Number,Order Time,Pickup Time,Queue Time,Total Time
Store A,01/01/2024 10:30 AM,Sedan,1,120,180,60,360
Store B,01/01/2024 11:15 AM,SUV,2,90,150,45,285
Store C,01/01/2024 02:45 PM,Truck,3,200,240,80,520
```

### Steps
1. **Direct Function Test:**
   ```bash
   curl -X POST "https://your-function-app.azurewebsites.net/api/csv/upload" \
        -H "Content-Type: text/csv" \
        -H "x-functions-key: YOUR_FUNCTION_KEY" \
        --data-binary @test-data.csv
   ```

2. **Expected Response:**
   ```json
   {
     "success": true,
     "correlationId": "guid-here",
     "message": "CSV processed successfully",
     "data": {
       "totalRowsProcessed": 3,
       "validRowsCount": 3,
       "invalidRowsCount": 0,
       "recordsInserted": 3,
       "processingTimeMs": 1234.56
     },
     "timestamp": "2024-01-01T12:00:00.000Z"
   }
   ```

3. **Database Verification:**
   ```sql
   SELECT TOP 10 * FROM EveryCarChamps 
   ORDER BY LaneDepartureTime DESC;
   ```

## 🔄 Test 3: Logic App Integration

### Purpose
Test the complete workflow from Logic App trigger to database insertion.

### Prerequisites
- Logic App is configured with Azure Functions action
- Blob storage container is set up for CSV input
- Function key is configured in Logic App

### Steps
1. **Upload Test File:**
   - Upload `test-data.csv` to the Logic App's input blob container
   - Monitor Logic App run history in Azure Portal

2. **Verify Logic App Execution:**
   - Check Logic App run status (should be "Succeeded")
   - Review each step's execution details
   - Verify HTTP response from Function App

3. **Database Verification:**
   ```sql
   SELECT COUNT(*) as RecordCount FROM EveryCarChamps 
   WHERE StoreName IN ('Store A', 'Store B', 'Store C');
   ```

## ❌ Test 4: Error Handling

### Purpose
Validate error handling with invalid data.

### Test Cases

#### 4.1 Invalid CSV Format
```csv
Invalid,Header,Structure
Data,Without,Proper,Columns
```

**Expected:** HTTP 400 with parsing errors

#### 4.2 Missing Required Fields
```csv
Store Name,Lane Departure Time,Car Type,Lane Number,Order Time,Pickup Time,Queue Time,Total Time
,01/01/2024 10:30 AM,Sedan,1,120,180,60,360
```

**Expected:** HTTP 400 with validation errors

#### 4.3 Invalid Date Format
```csv
Store Name,Lane Departure Time,Car Type,Lane Number,Order Time,Pickup Time,Queue Time,Total Time
Store A,invalid-date,Sedan,1,120,180,60,360
```

**Expected:** HTTP 400 with date parsing error

#### 4.4 Database Connection Failure
- Temporarily modify connection string to invalid value
- Test should return HTTP 503 with database connection error

## 📊 Test 5: Performance Testing

### Purpose
Validate performance with larger datasets.

### Steps
1. **Generate Large CSV:**
   - Create CSV with 100+ rows
   - Test processing time and memory usage

2. **Monitor Metrics:**
   - Check Application Insights for performance data
   - Verify processing stays within timeout limits
   - Monitor database connection pool usage

## 🔐 Test 6: Security Testing

### Purpose
Validate security configurations.

### Steps
1. **Test Without Function Key:**
   ```bash
   curl -X POST "https://your-function-app.azurewebsites.net/api/csv/upload" \
        -H "Content-Type: text/csv" \
        --data-binary @test-data.csv
   ```
   **Expected:** HTTP 401 Unauthorized

2. **Test with Invalid Function Key:**
   ```bash
   curl -X POST "https://your-function-app.azurewebsites.net/api/csv/upload" \
        -H "Content-Type: text/csv" \
        -H "x-functions-key: invalid-key" \
        --data-binary @test-data.csv
   ```
   **Expected:** HTTP 401 Unauthorized

## 📈 Monitoring and Alerting

### Application Insights Queries
```kusto
// Function execution times
requests
| where name == "ProcessCsvUpload"
| summarize avg(duration), max(duration), count() by bin(timestamp, 1h)

// Error rates
requests
| where name == "ProcessCsvUpload"
| summarize ErrorRate = countif(success == false) * 100.0 / count() by bin(timestamp, 1h)

// Custom metrics
customMetrics
| where name == "CsvProcessing"
| project timestamp, recordsInserted = toint(customDimensions.recordsInserted)
```

### Recommended Alerts
- Function execution failures > 5% in 15 minutes
- Average response time > 30 seconds
- Database connection failures
- Memory usage > 80%

## ✅ Production Readiness Checklist

After successful testing, verify:

- [ ] All tests pass consistently
- [ ] Error handling works as expected
- [ ] Performance meets requirements
- [ ] Security configurations are correct
- [ ] Monitoring and alerting are configured
- [ ] Logic App integration works end-to-end
- [ ] Database records are inserted correctly
- [ ] Function keys are secured and documented
- [ ] Backup and recovery procedures are in place

## 🚨 Troubleshooting Common Issues

### Issue: "Database connection failed"
- **Check:** SQL connection string format
- **Check:** Firewall rules on Azure SQL
- **Check:** Credentials and permissions

### Issue: "CSV parsing failed"
- **Check:** CSV format matches expected 8-column structure
- **Check:** Date format is `MM/dd/yyyy h:mm tt`
- **Check:** No special characters in data

### Issue: Logic App timeout
- **Check:** Function timeout settings in host.json
- **Check:** CSV file size vs MaxCsvRows setting
- **Check:** Database performance

### Issue: Duplicate key errors
- **Check:** CSV data for duplicate combinations
- **Check:** EveryCarChamps table composite key definition
- **Solution:** Clean duplicate data before processing

## 📞 Support Information

For production issues:
1. Check Application Insights logs first
2. Review Function App logs in Azure Portal
3. Verify database connectivity and performance
4. Check Logic App run history for integration issues

Remember to use correlation IDs from function responses to trace issues across systems.
