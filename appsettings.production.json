{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "__AzureWebJobsStorage__", "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated", "APPLICATIONINSIGHTS_CONNECTION_STRING": "__ApplicationInsights_ConnectionString__", "SqlConnectionString": "__SqlServer_ConnectionString__", "MaxCsvRows": "500", "EnableDetailedLogging": "false"}, "Host": {"CORS": "*", "CORSCredentials": false}, "ConnectionStrings": {"SqlConnectionString": "__SqlServer_ConnectionString__"}}