#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Deploy Scoreboard CSV Ingestor to an existing Azure Function App

.DESCRIPTION
    This script deploys the Scoreboard CSV Ingestor Azure Function to an existing Function App resource.
    It builds the project, creates a deployment package, and deploys using ZIP deployment.

.PARAMETER FunctionAppName
    The name of the existing Azure Function App to deploy to

.PARAMETER ResourceGroupName
    The name of the resource group containing the Function App

.PARAMETER SqlConnectionString
    The SQL Server connection string for the production database

.PARAMETER ApplicationInsightsConnectionString
    The Application Insights connection string for monitoring (optional)

.PARAMETER MaxCsvRows
    Maximum number of CSV rows to process (default: 500)

.PARAMETER EnableDetailedLogging
    Enable detailed logging for troubleshooting (default: false)

.EXAMPLE
    .\deploy-to-existing-function-app.ps1 -FunctionAppName "my-scoreboard-func" -ResourceGroupName "rg-production" -SqlConnectionString "Server=..."
#>

param(
    [Parameter(Mandatory = $true)]
    [string]$FunctionAppName,
    
    [Parameter(Mandatory = $true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory = $true)]
    [string]$SqlConnectionString,
    
    [Parameter(Mandatory = $false)]
    [string]$ApplicationInsightsConnectionString = "",
    
    [Parameter(Mandatory = $false)]
    [int]$MaxCsvRows = 500,
    
    [Parameter(Mandatory = $false)]
    [bool]$EnableDetailedLogging = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "🚀 Starting deployment to existing Azure Function App: $FunctionAppName" -ForegroundColor Green

try {
    # Check if Azure CLI is installed
    Write-Host "📋 Checking prerequisites..." -ForegroundColor Yellow
    
    if (!(Get-Command "az" -ErrorAction SilentlyContinue)) {
        throw "Azure CLI is not installed. Please install it from https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    }
    
    if (!(Get-Command "dotnet" -ErrorAction SilentlyContinue)) {
        throw ".NET SDK is not installed. Please install it from https://dotnet.microsoft.com/download"
    }

    # Check if logged into Azure
    Write-Host "🔐 Checking Azure login status..." -ForegroundColor Yellow
    $loginStatus = az account show --query "user.name" -o tsv 2>$null
    if (!$loginStatus) {
        Write-Host "Please log in to Azure..." -ForegroundColor Yellow
        az login
    } else {
        Write-Host "✅ Logged in as: $loginStatus" -ForegroundColor Green
    }

    # Verify Function App exists
    Write-Host "🔍 Verifying Function App exists..." -ForegroundColor Yellow
    $functionApp = az functionapp show --name $FunctionAppName --resource-group $ResourceGroupName --query "name" -o tsv 2>$null
    if (!$functionApp) {
        throw "Function App '$FunctionAppName' not found in resource group '$ResourceGroupName'"
    }
    Write-Host "✅ Function App found: $functionApp" -ForegroundColor Green

    # Clean and build the project
    Write-Host "🔨 Building the project..." -ForegroundColor Yellow
    dotnet clean
    dotnet build --configuration Release
    
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    Write-Host "✅ Build completed successfully" -ForegroundColor Green

    # Create deployment package
    Write-Host "📦 Creating deployment package..." -ForegroundColor Yellow
    $publishPath = "bin/Release/net8.0/publish"
    $zipPath = "deployment-package.zip"
    
    # Remove existing publish directory and zip file
    if (Test-Path $publishPath) {
        Remove-Item $publishPath -Recurse -Force
    }
    if (Test-Path $zipPath) {
        Remove-Item $zipPath -Force
    }
    
    # Publish the project
    dotnet publish --configuration Release --output $publishPath
    
    if ($LASTEXITCODE -ne 0) {
        throw "Publish failed"
    }

    # Create ZIP package
    if (Get-Command "Compress-Archive" -ErrorAction SilentlyContinue) {
        Compress-Archive -Path "$publishPath/*" -DestinationPath $zipPath -Force
    } else {
        # Fallback for systems without Compress-Archive
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::CreateFromDirectory($publishPath, $zipPath)
    }
    
    Write-Host "✅ Deployment package created: $zipPath" -ForegroundColor Green

    # Configure Function App settings
    Write-Host "⚙️ Configuring Function App settings..." -ForegroundColor Yellow
    
    $appSettings = @(
        "SqlConnectionString=$SqlConnectionString"
        "MaxCsvRows=$MaxCsvRows"
        "EnableDetailedLogging=$EnableDetailedLogging"
        "FUNCTIONS_WORKER_RUNTIME=dotnet-isolated"
    )
    
    if ($ApplicationInsightsConnectionString) {
        $appSettings += "APPLICATIONINSIGHTS_CONNECTION_STRING=$ApplicationInsightsConnectionString"
    }
    
    # Set app settings
    foreach ($setting in $appSettings) {
        $key, $value = $setting -split "=", 2
        az functionapp config appsettings set --name $FunctionAppName --resource-group $ResourceGroupName --settings "$key=$value" | Out-Null
    }
    
    Write-Host "✅ App settings configured" -ForegroundColor Green

    # Deploy the package
    Write-Host "🚀 Deploying to Azure Function App..." -ForegroundColor Yellow
    az functionapp deployment source config-zip --name $FunctionAppName --resource-group $ResourceGroupName --src $zipPath
    
    if ($LASTEXITCODE -ne 0) {
        throw "Deployment failed"
    }

    # Wait for deployment to complete
    Write-Host "⏳ Waiting for deployment to complete..." -ForegroundColor Yellow
    Start-Sleep -Seconds 30

    # Test the deployment
    Write-Host "🧪 Testing deployment..." -ForegroundColor Yellow
    $functionUrl = "https://$FunctionAppName.azurewebsites.net/api/health"
    
    try {
        $response = Invoke-RestMethod -Uri $functionUrl -Method Get -TimeoutSec 30
        if ($response.status -eq "healthy") {
            Write-Host "✅ Health check passed!" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Health check returned: $($response.status)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ Health check failed: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "This might be normal if the function is still starting up." -ForegroundColor Yellow
    }

    # Get function key for Logic App integration
    Write-Host "🔑 Retrieving function key..." -ForegroundColor Yellow
    try {
        $functionKeys = az functionapp keys list --name $FunctionAppName --resource-group $ResourceGroupName --query "functionKeys" -o json | ConvertFrom-Json
        if ($functionKeys.default) {
            Write-Host "✅ Function key retrieved successfully" -ForegroundColor Green
            Write-Host "🔗 Function URL: https://$FunctionAppName.azurewebsites.net/api/csv/upload" -ForegroundColor Cyan
            Write-Host "🔑 Function Key: $($functionKeys.default)" -ForegroundColor Cyan
            Write-Host "💡 Use this key in your Logic App HTTP action header: x-functions-key" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ Could not retrieve function key automatically. You can get it from the Azure Portal." -ForegroundColor Yellow
    }

    # Clean up
    Write-Host "🧹 Cleaning up..." -ForegroundColor Yellow
    if (Test-Path $zipPath) {
        Remove-Item $zipPath -Force
    }
    if (Test-Path $publishPath) {
        Remove-Item $publishPath -Recurse -Force
    }

    Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
    Write-Host "📊 Monitor your function at: https://portal.azure.com/#@/resource/subscriptions/[subscription]/resourceGroups/$ResourceGroupName/providers/Microsoft.Web/sites/$FunctionAppName" -ForegroundColor Cyan

} catch {
    Write-Host "❌ Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
